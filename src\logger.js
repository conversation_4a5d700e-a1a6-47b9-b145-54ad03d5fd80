const winston = require('winston');
const path = require('path');
const fs = require('fs');

// <PERSON><PERSON><PERSON> thư mục logs nếu chưa tồn tại
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss'
    }),
    winston.format.errors({ stack: true }),
    winston.format.printf(({ level, message, timestamp, stack }) => {
      return `${timestamp} [${level.toUpperCase()}]: ${stack || message}`;
    })
  ),
  transports: [
    // Ghi vào file
    new winston.transports.File({
      filename: path.join(logsDir, 'ssl-monitor.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // <PERSON><PERSON><PERSON> thị trên console
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

module.exports = logger;
