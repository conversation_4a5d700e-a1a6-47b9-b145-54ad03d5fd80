# SSL Monitor 🔒

Ứng dụng Node.js để theo dõi chứng chỉ SSL của các website và gửi thông báo khi sắp hết hạn.

## ✨ Tính năng

- 🔍 Kiểm tra chứng chỉ SSL của nhiều website
- ⏰ Cảnh báo khi còn 7 ngày hoặc 1 ngày (có thể tùy chỉnh)
- 📧 Gửi thông báo qua email
- 🔗 Gửi thông báo qua webhook (Slack, Discord, etc.)
- 📱 Hiển thị thông báo trên console
- 📅 Lên lịch kiểm tra tự động
- 📊 Logging chi tiết
- ⚙️ Cấu hình linh hoạt

## 🚀 Cài đặt

1. **Clone hoặc tải về dự án**
```bash
git clone <repository-url>
cd ssl-monitor
```

2. **Cài đặt dependencies**
```bash
npm install
```

3. **Cấu hình**
```bash
# Sao chép file cấu hình môi trường
cp .env.example .env

# Chỉnh sửa file .env với thông tin của bạn
# Chỉnh sửa file config/config.json để thêm website cần theo dõi
```

## ⚙️ Cấu hình

### 1. Thêm website cần theo dõi

Chỉnh sửa file `config/config.json`:

```json
{
  "websites": [
    {
      "name": "Website của tôi",
      "url": "example.com",
      "port": 443,
      "enabled": true
    }
  ]
}
```

### 2. Cấu hình email (tùy chọn)

Trong file `.env`:
```env
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>,<EMAIL>
```

**Lưu ý cho Gmail:**
- Bật 2FA cho tài khoản Gmail
- Tạo App Password thay vì dùng mật khẩu thường
- Sử dụng App Password trong EMAIL_PASS

### 3. Cấu hình webhook (tùy chọn)

Trong file `.env`:
```env
WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

## 🎯 Sử dụng

### Chạy kiểm tra một lần
```bash
npm run check
# hoặc
node src/check.js
```

### Chạy monitor liên tục
```bash
npm start
# hoặc
node src/index.js
```

### Chạy trong môi trường development
```bash
npm run dev
```

### Hiển thị trợ giúp
```bash
node src/index.js --help
```

## 📋 Các lệnh có sẵn

| Lệnh | Mô tả |
|------|-------|
| `npm start` | Chạy monitor liên tục theo lịch |
| `npm run check` | Kiểm tra một lần và thoát |
| `npm run dev` | Chạy với nodemon (auto-restart) |
| `npm test` | Chạy tests |

## 📊 Ví dụ output

```
🔒 ===== BÁO CÁO SSL CERTIFICATE =====
📅 Thời gian: 20/8/2025 09:00:00
⚠️  Số website cần chú ý: 2

1. Website A (example.com)
   ⏰ Còn 7 ngày (hết hạn: 27/8/2025)

2. Website B (test.com)
   🚨 Chứng chỉ đã HẾT HẠN từ 15/8/2025

=====================================
```

## 📁 Cấu trúc dự án

```
ssl-monitor/
├── config/
│   └── config.json          # Cấu hình chính
├── src/
│   ├── index.js            # File chính
│   ├── check.js            # Script kiểm tra một lần
│   ├── sslChecker.js       # Module kiểm tra SSL
│   ├── notificationManager.js # Module gửi thông báo
│   └── logger.js           # Module logging
├── logs/                   # Thư mục log files
├── .env                    # Cấu hình môi trường
├── .env.example           # Mẫu cấu hình môi trường
├── package.json
└── README.md
```

## 🔧 Tùy chỉnh nâng cao

### Thay đổi lịch kiểm tra

Trong `config/config.json`:
```json
{
  "schedule": {
    "cron": "0 9 * * *",           // 9:00 AM hàng ngày
    "timezone": "Asia/Ho_Chi_Minh"
  }
}
```

### Thay đổi ngày cảnh báo

```json
{
  "notifications": {
    "warningDays": [30, 7, 1]      // Cảnh báo khi còn 30, 7, 1 ngày
  }
}
```

### Cấu hình logging

```json
{
  "logging": {
    "level": "info",               // debug, info, warn, error
    "file": "logs/ssl-monitor.log"
  }
}
```

## 🐛 Troubleshooting

### Lỗi kết nối SSL
- Kiểm tra tên miền và port
- Đảm bảo website có SSL certificate
- Kiểm tra firewall/proxy

### Lỗi gửi email
- Kiểm tra thông tin SMTP
- Đảm bảo đã bật App Password cho Gmail
- Kiểm tra kết nối internet

### Lỗi webhook
- Kiểm tra URL webhook
- Đảm bảo service nhận webhook đang hoạt động

## 📝 License

MIT License

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request.
