{"websites": [{"name": "Google", "url": "google.com", "port": 443, "enabled": true}, {"name": "GitHub", "url": "github.com", "port": 443, "enabled": true}, {"name": "Example Website", "url": "example.com", "port": 443, "enabled": false}], "notifications": {"warningDays": [7, 1], "methods": {"email": {"enabled": false, "smtp": {"host": "smtp.gmail.com", "port": 587, "secure": false, "auth": {"user": "<EMAIL>", "pass": "your-app-password"}}, "from": "<EMAIL>", "to": ["<EMAIL>"]}, "webhook": {"enabled": false, "url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK", "method": "POST"}, "console": {"enabled": true}}}, "schedule": {"cron": "0 9 * * *", "timezone": "Asia/Ho_Chi_Minh"}, "logging": {"level": "info", "file": "logs/ssl-monitor.log"}}