# SSL Monitor - Google Apps Script 🔒

Ứng dụng theo dõi chứng chỉ SSL sử dụng Google Apps Script, hoàn toàn miễn phí và chạy trên cloud.

## ✨ Tính năng

- 🔍 Kiểm tra chứng chỉ SSL của nhiều website
- ⏰ Cảnh báo khi còn 30, 7, 1 ngày (có thể tùy chỉnh)
- 📧 Gửi email tự động qua Gmail
- 📱 G<PERSON>i thông bá<PERSON>ck (tù<PERSON> chọn)
- 📊 Lưu lịch sử vào Google Sheets (tùy chọn)
- ⚡ Chạy tự động theo lịch
- 🆓 Hoàn toàn miễn phí

## 🚀 Hướng dẫn cài đặt

### Bước 1: Tạo Google Apps Script Project

1. Truy cập [Google Apps Script](https://script.google.com)
2. Nhấn **"New Project"**
3. Đặt tên project: "SSL Monitor"

### Bước 2: Copy code

1. Xóa code mặc định trong file `Code.gs`
2. Copy toàn bộ nội dung từ file `Code.gs` trong thư mục này
3. Paste vào Google Apps Script Editor
4. Nhấn **Ctrl+S** để lưu

### Bước 3: Cấu hình

Chỉnh sửa phần `CONFIG` ở đầu file:

```javascript
const CONFIG = {
  // Thêm website của bạn
  websites: [
    { name: 'Website của tôi', url: 'example.com', enabled: true },
    { name: 'Website 2', url: 'mysite.com', enabled: true }
  ],
  
  // Cảnh báo khi còn bao nhiêu ngày
  warningDays: [30, 7, 1],
  
  // Email nhận thông báo
  email: {
    enabled: true,
    recipients: ['<EMAIL>'] // Thay bằng email của bạn
  }
};
```

### Bước 4: Cấp quyền

1. Nhấn **"Run"** để chạy hàm `checkAllSSLCertificates`
2. Cho phép quyền truy cập Gmail và UrlFetch khi được yêu cầu
3. Kiểm tra email để xem kết quả

### Bước 5: Thiết lập lịch tự động

1. Chạy hàm `setupDailyTrigger()` để thiết lập kiểm tra hàng ngày
2. Hoặc chạy `setupWeeklyTrigger()` để kiểm tra hàng tuần

## 📋 Các hàm có sẵn

| Hàm | Mô tả |
|-----|-------|
| `checkAllSSLCertificates()` | Kiểm tra tất cả website |
| `setupDailyTrigger()` | Thiết lập kiểm tra hàng ngày |
| `setupWeeklyTrigger()` | Thiết lập kiểm tra hàng tuần |
| `removeTriggers()` | Xóa tất cả lịch tự động |
| `testSingleWebsite()` | Test kiểm tra 1 website |
| `testEmail()` | Test gửi email |

## ⚙️ Cấu hình nâng cao

### Slack Integration

```javascript
slack: {
  enabled: true,
  webhookUrl: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
}
```

### Google Sheets Integration

```javascript
sheets: {
  enabled: true,
  spreadsheetId: 'YOUR_SPREADSHEET_ID'
}
```

## 📊 Ví dụ email thông báo

```
🔒 Báo cáo SSL Certificate
📅 Thời gian: 20/8/2025 09:00:00
⚠️ Số website cần chú ý: 2

┌─────────────────┬──────────────┬─────────────┬──────────┐
│ Website         │ Trạng thái   │ Hết hạn     │ Còn lại  │
├─────────────────┼──────────────┼─────────────┼──────────┤
│ ⚠️ Website A    │ Sắp hết hạn  │ 27/8/2025   │ 7 ngày   │
│ 🚨 Website B    │ Đã hết hạn   │ 15/8/2025   │ -5 ngày  │
└─────────────────┴──────────────┴─────────────┴──────────┘
```

## 🔧 Troubleshooting

### Lỗi "Script function not found"
- Đảm bảo đã lưu file và refresh trang

### Lỗi quyền truy cập
- Chạy lại hàm và cấp đầy đủ quyền cho Gmail và UrlFetch

### Không nhận được email
- Kiểm tra thư mục Spam
- Đảm bảo email trong `recipients` là chính xác

### SSL Labs API timeout
- API miễn phí có giới hạn, thử lại sau vài phút
- Hoặc giảm số lượng website kiểm tra cùng lúc

## 📝 Lưu ý quan trọng

1. **Giới hạn Google Apps Script:**
   - 6 phút execution time per run
   - 20,000 email/day
   - 20,000 UrlFetch calls/day

2. **SSL Labs API:**
   - Miễn phí nhưng có rate limit
   - Cần thời gian để scan (10-30 giây/website)

3. **Bảo mật:**
   - Không chia sẻ Slack webhook URL
   - Kiểm tra quyền truy cập định kỳ

## 🆚 So sánh với Node.js version

| Tính năng | Google Apps Script | Node.js |
|-----------|-------------------|---------|
| Chi phí | Miễn phí | Cần server |
| Thiết lập | Dễ dàng | Phức tạp hơn |
| Tích hợp Gmail | Sẵn có | Cần cấu hình |
| Hiệu suất | Giới hạn | Cao hơn |
| Tùy chỉnh | Hạn chế | Linh hoạt |

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request.

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra phần Troubleshooting
2. Xem logs trong Google Apps Script Editor
3. Tạo issue trên GitHub
