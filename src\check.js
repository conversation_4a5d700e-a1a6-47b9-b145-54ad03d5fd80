#!/usr/bin/env node

/**
 * Script để kiểm tra SSL một lần và hiển thị kết quả chi tiết
 */

require('dotenv').config();
const SSLMonitor = require('./index');

async function main() {
  const monitor = new SSLMonitor();
  
  try {
    const { results, warnings } = await monitor.checkOnce();
    
    // Hiển thị kết quả chi tiết
    console.log('\n📋 CHI TIẾT KẾT QUẢ:');
    console.log('='.repeat(60));
    
    results.forEach((result, index) => {
      console.log(`\n${index + 1}. ${result.name}`);
      console.log(`   🌐 URL: ${result.hostname}:${result.port}`);
      
      if (result.status === 'error') {
        console.log(`   ❌ Trạng thái: Lỗi`);
        console.log(`   📝 Chi tiết: ${result.error}`);
      } else {
        console.log(`   ✅ Trạng thái: Hoạt động`);
        console.log(`   📅 Hết hạn: ${result.validTo.toLocaleString('vi-VN')}`);
        console.log(`   ⏰ Còn lại: ${result.daysRemaining} ngày`);
        console.log(`   🏢 Nhà phát hành: ${result.issuer.CN || result.issuer.O || 'N/A'}`);
        console.log(`   🔒 Hợp lệ: ${result.isValid ? 'Có' : 'Không'}`);
        
        if (result.daysRemaining <= 7) {
          console.log(`   ⚠️  CẢNH BÁO: Sắp hết hạn!`);
        }
      }
    });
    
    console.log('\n' + '='.repeat(60));
    
    if (warnings.length > 0) {
      console.log(`\n🚨 CÓ ${warnings.length} WEBSITE CẦN CHÚ Ý:`);
      warnings.forEach(warning => {
        console.log(`   • ${warning.name} - ${warning.hostname}`);
      });
    } else {
      console.log('\n✅ TẤT CẢ WEBSITE ĐỀU BÌNH THƯỜNG');
    }
    
  } catch (error) {
    console.error('❌ Lỗi:', error.message);
    process.exit(1);
  }
}

main();
