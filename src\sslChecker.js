const tls = require('tls');
const { promisify } = require('util');

class SSLChecker {
  /**
   * <PERSON><PERSON><PERSON> tra chứng chỉ SSL của một website
   * @param {string} hostname - Tên miền website
   * @param {number} port - Cổng (mặc định 443)
   * @param {number} timeout - Timeout (mặc định 10000ms)
   * @returns {Promise<Object>} Thông tin chứng chỉ SSL
   */
  static async checkSSL(hostname, port = 443, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const options = {
        host: hostname,
        port: port,
        servername: hostname,
        rejectUnauthorized: false
      };

      const socket = tls.connect(options, () => {
        const cert = socket.getPeerCertificate();
        
        if (!cert || Object.keys(cert).length === 0) {
          socket.destroy();
          return reject(new Error('Không thể lấy thông tin chứng chỉ SSL'));
        }

        const result = {
          hostname,
          port,
          subject: cert.subject,
          issuer: cert.issuer,
          validFrom: new Date(cert.valid_from),
          validTo: new Date(cert.valid_to),
          daysRemaining: Math.floor((new Date(cert.valid_to) - new Date()) / (1000 * 60 * 60 * 24)),
          fingerprint: cert.fingerprint,
          serialNumber: cert.serialNumber,
          isValid: new Date() < new Date(cert.valid_to)
        };

        socket.destroy();
        resolve(result);
      });

      socket.on('error', (error) => {
        reject(new Error(`Lỗi kết nối SSL cho ${hostname}:${port} - ${error.message}`));
      });

      socket.setTimeout(timeout, () => {
        socket.destroy();
        reject(new Error(`Timeout khi kiểm tra SSL cho ${hostname}:${port}`));
      });
    });
  }

  /**
   * Kiểm tra nhiều website cùng lúc
   * @param {Array} websites - Danh sách website
   * @returns {Promise<Array>} Kết quả kiểm tra
   */
  static async checkMultiple(websites) {
    const results = [];
    
    for (const website of websites) {
      if (!website.enabled) {
        continue;
      }

      try {
        const result = await this.checkSSL(website.url, website.port);
        results.push({
          ...result,
          name: website.name,
          status: 'success'
        });
      } catch (error) {
        results.push({
          name: website.name,
          hostname: website.url,
          port: website.port,
          status: 'error',
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Lọc các chứng chỉ cần cảnh báo
   * @param {Array} results - Kết quả kiểm tra SSL
   * @param {Array} warningDays - Số ngày cảnh báo
   * @returns {Array} Danh sách cần cảnh báo
   */
  static filterWarnings(results, warningDays = [7, 1]) {
    return results.filter(result => {
      if (result.status === 'error') {
        return true; // Luôn cảnh báo lỗi
      }

      if (!result.isValid) {
        return true; // Chứng chỉ đã hết hạn
      }

      return warningDays.includes(result.daysRemaining);
    });
  }
}

module.exports = SSLChecker;
