# Hướng dẫn thiết lập chi tiết - SSL Monitor

## 📋 Checklist thiết lập

- [ ] Tạo Google Apps Script project
- [ ] Copy code và cấu hình
- [ ] Test chạy lần đầu
- [ ] Cấp quyền truy cập
- [ ] Thiết lập lịch tự động
- [ ] C<PERSON><PERSON> hì<PERSON> (tù<PERSON> chọn)
- [ ] Cấu hình Google Sheets (tùy chọn)

## 🎯 Hướng dẫn từng bước

### Bước 1: Tạo Project

1. **T<PERSON>y cập Google Apps Script**
   - Mở [script.google.com](https://script.google.com)
   - Đăng nhập bằng tài khoản Google

2. **Tạo project mới**
   - Nhấn "New Project"
   - Đặt tên: "SSL Monitor"
   - Nhấn "Untitled project" để đổi tên

### Bước 2: Setup Code

1. **Xóa code mặc định**
   ```javascript
   function myFunction() {
     // <PERSON><PERSON><PERSON> dòng này
   }
   ```

2. **Copy code từ file Code.gs**
   - Copy toàn bộ nội dung
   - Paste vào editor
   - <PERSON><PERSON><PERSON>n Ctrl+S để lưu

3. **Cấu hình website**
   ```javascript
   websites: [
     { name: 'Website chính', url: 'yoursite.com', enabled: true },
     { name: 'Website phụ', url: 'subdomain.yoursite.com', enabled: true },
     // Thêm nhiều website khác...
   ]
   ```

4. **Cấu hình email**
   ```javascript
   email: {
     enabled: true,
     recipients: [
       '<EMAIL>',
       '<EMAIL>'
     ]
   }
   ```

### Bước 3: Test lần đầu

1. **Chọn hàm test**
   - Dropdown: chọn `testSingleWebsite`
   - Nhấn "Run"

2. **Cấp quyền**
   - Nhấn "Review permissions"
   - Chọn tài khoản Google
   - Nhấn "Advanced" → "Go to SSL Monitor (unsafe)"
   - Nhấn "Allow"

3. **Kiểm tra logs**
   - Nhấn "Execution transcript"
   - Xem kết quả trong console

### Bước 4: Test email

1. **Chạy test email**
   - Chọn hàm `testEmail`
   - Nhấn "Run"

2. **Kiểm tra email**
   - Mở Gmail
   - Tìm email từ chính bạn
   - Kiểm tra cả thư mục Spam

### Bước 5: Chạy kiểm tra thực

1. **Chạy hàm chính**
   - Chọn `checkAllSSLCertificates`
   - Nhấn "Run"

2. **Kiểm tra kết quả**
   - Xem logs trong "Execution transcript"
   - Kiểm tra email nhận được

### Bước 6: Thiết lập lịch tự động

1. **Chạy setup trigger**
   - Chọn `setupDailyTrigger` (hàng ngày)
   - Hoặc `setupWeeklyTrigger` (hàng tuần)
   - Nhấn "Run"

2. **Kiểm tra trigger**
   - Nhấn biểu tượng đồng hồ ⏰ bên trái
   - Xem danh sách triggers đã tạo

## ⚙️ Cấu hình nâng cao

### Slack Integration

1. **Tạo Slack Webhook**
   - Vào Slack workspace
   - Apps → Incoming Webhooks
   - Add to Slack
   - Chọn channel
   - Copy Webhook URL

2. **Cấu hình trong code**
   ```javascript
   slack: {
     enabled: true,
     webhookUrl: '*****************************************************************************'
   }
   ```

### Google Sheets Integration

1. **Tạo Google Sheets**
   - Tạo spreadsheet mới
   - Copy ID từ URL: `docs.google.com/spreadsheets/d/{ID}/edit`

2. **Cấu hình trong code**
   ```javascript
   sheets: {
     enabled: true,
     spreadsheetId: '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms'
   }
   ```

## 🔧 Tùy chỉnh nâng cao

### Thay đổi lịch kiểm tra

```javascript
// Hàng ngày lúc 8:00 AM
ScriptApp.newTrigger('checkAllSSLCertificates')
  .timeBased()
  .everyDays(1)
  .atHour(8)
  .create();

// Mỗi 12 tiếng
ScriptApp.newTrigger('checkAllSSLCertificates')
  .timeBased()
  .everyHours(12)
  .create();

// Thứ 2 và thứ 6 hàng tuần
ScriptApp.newTrigger('checkAllSSLCertificates')
  .timeBased()
  .onWeekDay(ScriptApp.WeekDay.MONDAY)
  .atHour(9)
  .create();
```

### Tùy chỉnh ngày cảnh báo

```javascript
// Cảnh báo nhiều mốc thời gian
warningDays: [90, 30, 14, 7, 3, 1]

// Chỉ cảnh báo gấp
warningDays: [7, 1]

// Cảnh báo sớm
warningDays: [60, 30, 7]
```

### Tùy chỉnh email template

```javascript
// Trong hàm generateEmailHTML(), thêm CSS
const css = `
<style>
  .warning { background-color: #fff3cd; }
  .danger { background-color: #f8d7da; }
  .success { background-color: #d4edda; }
</style>
`;
```

## 🚨 Xử lý lỗi thường gặp

### Lỗi "ReferenceError: CONFIG is not defined"
```javascript
// Đảm bảo CONFIG được khai báo ở đầu file
const CONFIG = {
  // ... cấu hình
};
```

### Lỗi "Exception: Request failed for https://api.ssllabs.com"
```javascript
// Thêm retry logic
function getSSLInfoViaAPI(hostname, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      // ... existing code
      return result;
    } catch (error) {
      if (i === retries - 1) throw error;
      Utilities.sleep(5000); // Chờ 5 giây
    }
  }
}
```

### Lỗi "Exceeded maximum execution time"
```javascript
// Giảm số website kiểm tra cùng lúc
const BATCH_SIZE = 5;
for (let i = 0; i < websites.length; i += BATCH_SIZE) {
  const batch = websites.slice(i, i + BATCH_SIZE);
  // Process batch
  if (i + BATCH_SIZE < websites.length) {
    Utilities.sleep(2000); // Nghỉ giữa các batch
  }
}
```

## 📊 Monitoring và Maintenance

### Kiểm tra logs định kỳ
1. Vào Google Apps Script
2. Nhấn "Executions" 
3. Xem lịch sử chạy và lỗi

### Backup cấu hình
```javascript
// Export cấu hình ra file
function exportConfig() {
  const configJson = JSON.stringify(CONFIG, null, 2);
  console.log('Backup config:', configJson);
}
```

### Update code
1. Backup cấu hình hiện tại
2. Copy code mới
3. Restore cấu hình
4. Test lại

## 🎯 Best Practices

1. **Bắt đầu nhỏ**: Test với 2-3 website trước
2. **Monitor logs**: Kiểm tra execution history thường xuyên  
3. **Backup**: Lưu cấu hình quan trọng
4. **Test email**: Chạy testEmail() sau mỗi thay đổi
5. **Rate limiting**: Không kiểm tra quá nhiều website cùng lúc
