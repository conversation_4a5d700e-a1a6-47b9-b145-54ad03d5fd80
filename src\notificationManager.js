const nodemailer = require('nodemailer');
const axios = require('axios');
const logger = require('./logger');

class NotificationManager {
  constructor(config) {
    this.config = config;
    this.emailTransporter = null;
    
    if (config.methods.email.enabled) {
      this.setupEmailTransporter();
    }
  }

  setupEmailTransporter() {
    try {
      this.emailTransporter = nodemailer.createTransporter(this.config.methods.email.smtp);
    } catch (error) {
      logger.error('Lỗi thiết lập email transporter:', error);
    }
  }

  /**
   * G<PERSON>i thông báo qua email
   */
  async sendEmailNotification(warnings) {
    if (!this.config.methods.email.enabled || !this.emailTransporter) {
      return;
    }

    try {
      const subject = `🔒 Cảnh báo SSL Certificate - ${warnings.length} website cần chú ý`;
      const html = this.generateEmailHTML(warnings);

      const mailOptions = {
        from: this.config.methods.email.from,
        to: this.config.methods.email.to,
        subject: subject,
        html: html
      };

      await this.emailTransporter.sendMail(mailOptions);
      logger.info(`Đã gửi email thông báo cho ${this.config.methods.email.to.length} người nhận`);
    } catch (error) {
      logger.error('Lỗi gửi email:', error);
    }
  }

  /**
   * Gửi thông báo qua webhook (Slack, Discord, etc.)
   */
  async sendWebhookNotification(warnings) {
    if (!this.config.methods.webhook.enabled) {
      return;
    }

    try {
      const message = this.generateWebhookMessage(warnings);
      
      await axios({
        method: this.config.methods.webhook.method || 'POST',
        url: this.config.methods.webhook.url,
        data: message,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      logger.info('Đã gửi thông báo webhook');
    } catch (error) {
      logger.error('Lỗi gửi webhook:', error);
    }
  }

  /**
   * Hiển thị thông báo trên console
   */
  sendConsoleNotification(warnings) {
    if (!this.config.methods.console.enabled) {
      return;
    }

    console.log('\n🔒 ===== BÁO CÁO SSL CERTIFICATE =====');
    console.log(`📅 Thời gian: ${new Date().toLocaleString('vi-VN')}`);
    console.log(`⚠️  Số website cần chú ý: ${warnings.length}\n`);

    warnings.forEach((warning, index) => {
      console.log(`${index + 1}. ${warning.name} (${warning.hostname})`);
      
      if (warning.status === 'error') {
        console.log(`   ❌ Lỗi: ${warning.error}`);
      } else if (!warning.isValid) {
        console.log(`   🚨 Chứng chỉ đã HẾT HẠN từ ${warning.validTo.toLocaleDateString('vi-VN')}`);
      } else {
        console.log(`   ⏰ Còn ${warning.daysRemaining} ngày (hết hạn: ${warning.validTo.toLocaleDateString('vi-VN')})`);
      }
      console.log('');
    });

    console.log('=====================================\n');
  }

  /**
   * Gửi tất cả các loại thông báo
   */
  async sendAllNotifications(warnings) {
    if (warnings.length === 0) {
      logger.info('Không có cảnh báo SSL nào');
      return;
    }

    logger.info(`Gửi thông báo cho ${warnings.length} website`);

    // Gửi đồng thời tất cả các loại thông báo
    await Promise.allSettled([
      this.sendEmailNotification(warnings),
      this.sendWebhookNotification(warnings),
      this.sendConsoleNotification(warnings)
    ]);
  }

  generateEmailHTML(warnings) {
    let html = `
      <h2>🔒 Báo cáo SSL Certificate</h2>
      <p><strong>Thời gian:</strong> ${new Date().toLocaleString('vi-VN')}</p>
      <p><strong>Số website cần chú ý:</strong> ${warnings.length}</p>
      <table border="1" style="border-collapse: collapse; width: 100%;">
        <thead>
          <tr style="background-color: #f2f2f2;">
            <th style="padding: 8px;">Website</th>
            <th style="padding: 8px;">Trạng thái</th>
            <th style="padding: 8px;">Ngày hết hạn</th>
            <th style="padding: 8px;">Còn lại</th>
          </tr>
        </thead>
        <tbody>
    `;

    warnings.forEach(warning => {
      const statusIcon = warning.status === 'error' ? '❌' : 
                        !warning.isValid ? '🚨' : '⚠️';
      const status = warning.status === 'error' ? 'Lỗi kết nối' :
                    !warning.isValid ? 'Đã hết hạn' : 'Sắp hết hạn';
      const expiry = warning.validTo ? warning.validTo.toLocaleDateString('vi-VN') : 'N/A';
      const remaining = warning.daysRemaining !== undefined ? 
                       `${warning.daysRemaining} ngày` : 'N/A';

      html += `
        <tr>
          <td style="padding: 8px;">${statusIcon} ${warning.name}<br><small>${warning.hostname}</small></td>
          <td style="padding: 8px;">${status}</td>
          <td style="padding: 8px;">${expiry}</td>
          <td style="padding: 8px;">${remaining}</td>
        </tr>
      `;
    });

    html += `
        </tbody>
      </table>
      <p><small>Được tạo bởi SSL Monitor</small></p>
    `;

    return html;
  }

  generateWebhookMessage(warnings) {
    // Format cho Slack
    const text = `🔒 *Cảnh báo SSL Certificate*\n📅 ${new Date().toLocaleString('vi-VN')}\n⚠️ ${warnings.length} website cần chú ý`;
    
    const attachments = warnings.map(warning => {
      const color = warning.status === 'error' ? 'danger' : 
                   !warning.isValid ? 'danger' : 'warning';
      
      return {
        color: color,
        fields: [
          {
            title: warning.name,
            value: `${warning.hostname}\n${warning.status === 'error' ? warning.error : 
                   `Hết hạn: ${warning.validTo?.toLocaleDateString('vi-VN')}\nCòn: ${warning.daysRemaining} ngày`}`,
            short: true
          }
        ]
      };
    });

    return {
      text: text,
      attachments: attachments
    };
  }
}

module.exports = NotificationManager;
