/**
 * Advanced SSL Checker - <PERSON><PERSON><PERSON> bản nâng cao với nhiều tính năng
 * Sử dụng multiple APIs và fallback methods
 */

/**
 * SSL Checker nâng cao với nhiều phương pháp kiểm tra
 */
class AdvancedSSLChecker {
  
  /**
   * Kiểm tra SSL với multiple methods
   */
  static checkSSLAdvanced(hostname) {
    const methods = [
      () => this.checkViaSSLLabs(hostname),
      () => this.checkViaSSLShopper(hostname),
      () => this.checkViaDirectConnection(hostname),
      () => this.checkViaSimpleHTTPS(hostname)
    ];
    
    for (const method of methods) {
      try {
        const result = method();
        if (result && result.validTo) {
          return result;
        }
      } catch (error) {
        console.log(`Method failed for ${hostname}:`, error.message);
      }
    }
    
    throw new Error(`Không thể kiểm tra SSL cho ${hostname} bằng bất kỳ phương pháp nào`);
  }
  
  /**
   * <PERSON>ểm tra qua SSL Labs API
   */
  static checkViaSSLLabs(hostname) {
    const apiUrl = `https://api.ssllabs.com/api/v3/analyze?host=${hostname}&publish=off&all=done&ignoreMismatch=on`;
    
    // Bắt đầu scan
    let response = UrlFetchApp.fetch(apiUrl, { muteHttpExceptions: true });
    let data = JSON.parse(response.getContentText());
    
    // Chờ kết quả
    let attempts = 0;
    while (data.status !== 'READY' && attempts < 8) {
      console.log(`⏳ SSL Labs scan ${hostname}: ${data.status} (${attempts + 1}/8)`);
      Utilities.sleep(15000); // Chờ 15 giây
      
      response = UrlFetchApp.fetch(apiUrl, { muteHttpExceptions: true });
      data = JSON.parse(response.getContentText());
      attempts++;
    }
    
    if (data.status !== 'READY' || !data.endpoints || data.endpoints.length === 0) {
      throw new Error('SSL Labs scan không hoàn thành');
    }
    
    const endpoint = data.endpoints[0];
    const cert = endpoint.details.cert;
    
    return this.formatSSLResult(hostname, {
      validFrom: new Date(cert.notBefore),
      validTo: new Date(cert.notAfter),
      issuer: cert.issuerLabel,
      subject: cert.subject,
      grade: endpoint.grade,
      method: 'SSL Labs'
    });
  }
  
  /**
   * Kiểm tra qua SSL Shopper API (alternative)
   */
  static checkViaSSLShopper(hostname) {
    const apiUrl = `https://www.sslshopper.com/ssl-checker.html?hostname=${hostname}`;
    
    try {
      const response = UrlFetchApp.fetch(apiUrl, { 
        muteHttpExceptions: true,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; SSL-Monitor/1.0)'
        }
      });
      
      const html = response.getContentText();
      
      // Parse HTML để lấy thông tin SSL (regex parsing)
      const validToMatch = html.match(/Valid Until[^>]*>([^<]+)</i);
      const issuerMatch = html.match(/Issued By[^>]*>([^<]+)</i);
      
      if (validToMatch) {
        const validTo = new Date(validToMatch[1]);
        return this.formatSSLResult(hostname, {
          validTo: validTo,
          issuer: issuerMatch ? issuerMatch[1] : 'Unknown',
          method: 'SSL Shopper'
        });
      }
      
      throw new Error('Không thể parse SSL info từ SSL Shopper');
      
    } catch (error) {
      throw new Error(`SSL Shopper failed: ${error.message}`);
    }
  }
  
  /**
   * Kiểm tra trực tiếp qua HTTPS connection
   */
  static checkViaDirectConnection(hostname) {
    try {
      // Sử dụng một service để lấy certificate info
      const apiUrl = `https://crt.sh/?q=${hostname}&output=json&limit=1`;
      
      const response = UrlFetchApp.fetch(apiUrl, { muteHttpExceptions: true });
      const data = JSON.parse(response.getContentText());
      
      if (data && data.length > 0) {
        const cert = data[0];
        return this.formatSSLResult(hostname, {
          validFrom: new Date(cert.not_before),
          validTo: new Date(cert.not_after),
          issuer: cert.issuer_name,
          subject: cert.name_value,
          method: 'crt.sh'
        });
      }
      
      throw new Error('Không tìm thấy certificate trong crt.sh');
      
    } catch (error) {
      throw new Error(`Direct connection failed: ${error.message}`);
    }
  }
  
  /**
   * Kiểm tra đơn giản qua HTTPS request
   */
  static checkViaSimpleHTTPS(hostname) {
    try {
      const url = `https://${hostname}`;
      const response = UrlFetchApp.fetch(url, {
        method: 'HEAD',
        muteHttpExceptions: true,
        followRedirects: false
      });
      
      if (response.getResponseCode() < 400) {
        // Ước tính thời gian hết hạn (thường là 90 ngày cho Let's Encrypt)
        const estimatedExpiry = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000);
        
        return this.formatSSLResult(hostname, {
          validTo: estimatedExpiry,
          issuer: 'Unknown (Estimated)',
          method: 'Simple HTTPS',
          note: 'Thông tin ước tính - không thể lấy chi tiết certificate'
        });
      }
      
      throw new Error(`HTTP ${response.getResponseCode()}`);
      
    } catch (error) {
      throw new Error(`Simple HTTPS failed: ${error.message}`);
    }
  }
  
  /**
   * Format kết quả SSL thành format chuẩn
   */
  static formatSSLResult(hostname, data) {
    const now = new Date();
    const validTo = data.validTo;
    const daysRemaining = validTo ? Math.floor((validTo - now) / (1000 * 60 * 60 * 24)) : null;
    
    return {
      hostname: hostname,
      validFrom: data.validFrom || null,
      validTo: validTo,
      daysRemaining: daysRemaining,
      isValid: validTo ? now < validTo : false,
      issuer: data.issuer || 'Unknown',
      subject: data.subject || hostname,
      grade: data.grade || null,
      method: data.method || 'Unknown',
      note: data.note || null
    };
  }
}

/**
 * Batch SSL Checker - Kiểm tra nhiều website với rate limiting
 */
class BatchSSLChecker {
  
  static async checkBatch(websites, batchSize = 3, delayMs = 5000) {
    const results = [];
    
    for (let i = 0; i < websites.length; i += batchSize) {
      const batch = websites.slice(i, i + batchSize);
      console.log(`🔄 Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(websites.length/batchSize)}`);
      
      // Process batch
      for (const website of batch) {
        if (!website.enabled) continue;
        
        try {
          const sslInfo = AdvancedSSLChecker.checkSSLAdvanced(website.url);
          results.push({
            ...website,
            ...sslInfo,
            status: 'success'
          });
          
          console.log(`✅ ${website.name}: ${sslInfo.daysRemaining} ngày (${sslInfo.method})`);
          
        } catch (error) {
          results.push({
            ...website,
            status: 'error',
            error: error.message
          });
          
          console.error(`❌ ${website.name}: ${error.message}`);
        }
      }
      
      // Delay between batches
      if (i + batchSize < websites.length) {
        console.log(`⏸️ Waiting ${delayMs/1000}s before next batch...`);
        Utilities.sleep(delayMs);
      }
    }
    
    return results;
  }
}

/**
 * SSL Monitor với caching
 */
class CachedSSLMonitor {
  
  static getCacheKey(hostname) {
    return `ssl_${hostname}`;
  }
  
  static getCachedResult(hostname) {
    try {
      const cache = CacheService.getScriptCache();
      const cached = cache.get(this.getCacheKey(hostname));
      
      if (cached) {
        const data = JSON.parse(cached);
        // Cache valid for 6 hours
        if (Date.now() - data.timestamp < 6 * 60 * 60 * 1000) {
          console.log(`📋 Using cached result for ${hostname}`);
          return data.result;
        }
      }
    } catch (error) {
      console.log(`Cache error for ${hostname}:`, error.message);
    }
    
    return null;
  }
  
  static setCachedResult(hostname, result) {
    try {
      const cache = CacheService.getScriptCache();
      const data = {
        result: result,
        timestamp: Date.now()
      };
      
      // Cache for 6 hours
      cache.put(this.getCacheKey(hostname), JSON.stringify(data), 21600);
      console.log(`💾 Cached result for ${hostname}`);
      
    } catch (error) {
      console.log(`Cache save error for ${hostname}:`, error.message);
    }
  }
  
  static checkWithCache(hostname) {
    // Try cache first
    const cached = this.getCachedResult(hostname);
    if (cached) {
      return cached;
    }
    
    // Check fresh
    const result = AdvancedSSLChecker.checkSSLAdvanced(hostname);
    
    // Cache result
    this.setCachedResult(hostname, result);
    
    return result;
  }
}

/**
 * Hàm chính sử dụng advanced checker
 */
function checkAllSSLAdvanced() {
  console.log('🚀 Bắt đầu kiểm tra SSL (Advanced Mode)...');
  
  const results = BatchSSLChecker.checkBatch(CONFIG.websites, 3, 5000);
  const warnings = results.filter(result => {
    if (result.status === 'error') return true;
    if (!result.isValid) return true;
    return CONFIG.warningDays.includes(result.daysRemaining);
  });
  
  console.log(`📊 Kết quả: ${results.length} websites, ${warnings.length} warnings`);
  
  // Gửi thông báo
  if (warnings.length > 0) {
    const notificationManager = new NotificationManager(CONFIG.notifications);
    notificationManager.sendAllNotifications(warnings, results);
  }
  
  return { results, warnings };
}

/**
 * Test advanced checker với một website
 */
function testAdvancedChecker() {
  const hostname = 'google.com';
  
  try {
    console.log(`🔍 Testing advanced SSL check for ${hostname}...`);
    const result = AdvancedSSLChecker.checkSSLAdvanced(hostname);
    console.log('✅ Result:', result);
    return result;
  } catch (error) {
    console.error('❌ Error:', error.message);
    return null;
  }
}
