/**
 * SSL Certificate Monitor - Google Apps Script
 * Theo dõi chứng chỉ SSL và gửi thông báo khi sắp hết hạn
 */

// ===== CẤU HÌNH =====
const CONFIG = {
  // Danh sách website cần theo dõi
  websites: [
    { name: 'Google', url: 'google.com', enabled: true },
    { name: 'GitHub', url: 'github.com', enabled: true },
    { name: 'Facebook', url: 'facebook.com', enabled: true },
    // Thêm website của bạn vào đây
    // { name: 'Website của tôi', url: 'example.com', enabled: true }
  ],
  
  // Cảnh báo khi còn bao nhiêu ngày
  warningDays: [30, 7, 1],
  
  // Cấu hình email
  email: {
    enabled: true,
    recipients: ['<EMAIL>'], // Thay bằng email của bạn
    subject: '🔒 Cảnh báo SSL Certificate'
  },
  
  // C<PERSON><PERSON> hình Slack webhook (tùy chọn)
  slack: {
    enabled: false,
    webhookUrl: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
  },
  
  // Cấu hình Google Sheets để lưu lịch sử (tùy chọn)
  sheets: {
    enabled: false,
    spreadsheetId: 'YOUR_SPREADSHEET_ID'
  }
};

/**
 * Hàm chính - kiểm tra tất cả SSL certificates
 */
function checkAllSSLCertificates() {
  console.log('🚀 Bắt đầu kiểm tra SSL certificates...');
  
  const results = [];
  const warnings = [];
  
  // Kiểm tra từng website
  CONFIG.websites.forEach(website => {
    if (!website.enabled) {
      console.log(`⏭️ Bỏ qua ${website.name} (disabled)`);
      return;
    }
    
    try {
      const sslInfo = checkSSLCertificate(website.url);
      const result = {
        ...website,
        ...sslInfo,
        status: 'success'
      };
      
      results.push(result);
      
      // Kiểm tra xem có cần cảnh báo không
      if (shouldWarn(result)) {
        warnings.push(result);
      }
      
      console.log(`✅ ${website.name}: ${result.daysRemaining} ngày còn lại`);
      
    } catch (error) {
      const errorResult = {
        ...website,
        status: 'error',
        error: error.message
      };
      
      results.push(errorResult);
      warnings.push(errorResult);
      
      console.error(`❌ ${website.name}: ${error.message}`);
    }
  });
  
  // Gửi thông báo nếu có cảnh báo
  if (warnings.length > 0) {
    sendNotifications(warnings, results);
  } else {
    console.log('✅ Tất cả SSL certificates đều bình thường');
  }
  
  // Lưu vào Google Sheets nếu được bật
  if (CONFIG.sheets.enabled) {
    saveToSheets(results);
  }
  
  return { results, warnings };
}

/**
 * Kiểm tra SSL certificate của một website
 */
function checkSSLCertificate(hostname) {
  try {
    // Sử dụng UrlFetchApp để lấy thông tin SSL
    const url = `https://${hostname}`;
    const response = UrlFetchApp.fetch(url, {
      method: 'HEAD',
      muteHttpExceptions: true,
      followRedirects: false
    });
    
    // Lấy thông tin certificate từ response headers
    // Lưu ý: Google Apps Script có hạn chế trong việc truy cập certificate details
    // Chúng ta sẽ sử dụng API bên thứ 3 hoặc workaround
    
    return getSSLInfoViaAPI(hostname);
    
  } catch (error) {
    throw new Error(`Không thể kiểm tra SSL cho ${hostname}: ${error.message}`);
  }
}

/**
 * Lấy thông tin SSL qua API bên thứ 3 (SSL Labs hoặc tương tự)
 */
function getSSLInfoViaAPI(hostname) {
  try {
    // Sử dụng SSL Labs API (miễn phí)
    const apiUrl = `https://api.ssllabs.com/api/v3/analyze?host=${hostname}&publish=off&all=done`;
    
    // Gọi API để bắt đầu scan
    let response = UrlFetchApp.fetch(apiUrl, { muteHttpExceptions: true });
    let data = JSON.parse(response.getContentText());
    
    // Chờ kết quả (SSL Labs cần thời gian để scan)
    let attempts = 0;
    while (data.status !== 'READY' && attempts < 10) {
      console.log(`⏳ Đang chờ kết quả SSL scan cho ${hostname}... (${attempts + 1}/10)`);
      Utilities.sleep(10000); // Chờ 10 giây
      
      response = UrlFetchApp.fetch(apiUrl, { muteHttpExceptions: true });
      data = JSON.parse(response.getContentText());
      attempts++;
    }
    
    if (data.status !== 'READY' || !data.endpoints || data.endpoints.length === 0) {
      throw new Error('Không thể lấy thông tin SSL từ SSL Labs API');
    }
    
    const endpoint = data.endpoints[0];
    const certInfo = endpoint.details.cert;
    
    const validTo = new Date(certInfo.notAfter);
    const now = new Date();
    const daysRemaining = Math.floor((validTo - now) / (1000 * 60 * 60 * 24));
    
    return {
      hostname: hostname,
      validFrom: new Date(certInfo.notBefore),
      validTo: validTo,
      daysRemaining: daysRemaining,
      isValid: now < validTo,
      issuer: certInfo.issuerLabel,
      subject: certInfo.subject
    };
    
  } catch (error) {
    // Fallback: Sử dụng phương pháp đơn giản hơn
    return getSSLInfoSimple(hostname);
  }
}

/**
 * Phương pháp đơn giản để ước tính thông tin SSL
 */
function getSSLInfoSimple(hostname) {
  try {
    // Thử kết nối HTTPS và ước tính dựa trên response
    const url = `https://${hostname}`;
    const response = UrlFetchApp.fetch(url, {
      method: 'HEAD',
      muteHttpExceptions: true
    });
    
    if (response.getResponseCode() >= 400) {
      throw new Error(`HTTP ${response.getResponseCode()}`);
    }
    
    // Không thể lấy chính xác ngày hết hạn, trả về thông tin cơ bản
    return {
      hostname: hostname,
      validFrom: new Date(),
      validTo: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // Giả định 90 ngày
      daysRemaining: 90,
      isValid: true,
      issuer: 'Unknown',
      subject: hostname,
      note: 'Thông tin ước tính - không thể lấy chi tiết certificate'
    };
    
  } catch (error) {
    throw new Error(`Lỗi kết nối: ${error.message}`);
  }
}

/**
 * Kiểm tra xem có cần cảnh báo không
 */
function shouldWarn(result) {
  if (result.status === 'error') {
    return true;
  }
  
  if (!result.isValid) {
    return true; // Đã hết hạn
  }
  
  return CONFIG.warningDays.includes(result.daysRemaining);
}

/**
 * Gửi tất cả các loại thông báo
 */
function sendNotifications(warnings, allResults) {
  console.log(`📢 Gửi thông báo cho ${warnings.length} website cần chú ý`);
  
  // Gửi email
  if (CONFIG.email.enabled) {
    sendEmailNotification(warnings, allResults);
  }
  
  // Gửi Slack
  if (CONFIG.slack.enabled) {
    sendSlackNotification(warnings);
  }
}

/**
 * Gửi email thông báo
 */
function sendEmailNotification(warnings, allResults) {
  try {
    const subject = `${CONFIG.email.subject} - ${warnings.length} website cần chú ý`;
    const htmlBody = generateEmailHTML(warnings, allResults);
    
    CONFIG.email.recipients.forEach(recipient => {
      GmailApp.sendEmail(recipient, subject, '', {
        htmlBody: htmlBody
      });
    });
    
    console.log(`📧 Đã gửi email cho ${CONFIG.email.recipients.length} người nhận`);
    
  } catch (error) {
    console.error('❌ Lỗi gửi email:', error);
  }
}

/**
 * Tạo nội dung HTML cho email
 */
function generateEmailHTML(warnings, allResults) {
  let html = `
    <h2>🔒 Báo cáo SSL Certificate</h2>
    <p><strong>Thời gian:</strong> ${new Date().toLocaleString('vi-VN')}</p>
    <p><strong>Tổng số website:</strong> ${allResults.length}</p>
    <p><strong>Cần chú ý:</strong> ${warnings.length}</p>
    
    <h3>⚠️ Website cần chú ý:</h3>
    <table border="1" style="border-collapse: collapse; width: 100%; margin-bottom: 20px;">
      <thead>
        <tr style="background-color: #f2f2f2;">
          <th style="padding: 8px;">Website</th>
          <th style="padding: 8px;">Trạng thái</th>
          <th style="padding: 8px;">Hết hạn</th>
          <th style="padding: 8px;">Còn lại</th>
        </tr>
      </thead>
      <tbody>
  `;
  
  warnings.forEach(warning => {
    const statusIcon = warning.status === 'error' ? '❌' : 
                      !warning.isValid ? '🚨' : '⚠️';
    const status = warning.status === 'error' ? 'Lỗi kết nối' :
                  !warning.isValid ? 'Đã hết hạn' : 'Sắp hết hạn';
    const expiry = warning.validTo ? warning.validTo.toLocaleDateString('vi-VN') : 'N/A';
    const remaining = warning.daysRemaining !== undefined ? 
                     `${warning.daysRemaining} ngày` : 'N/A';
    
    html += `
      <tr>
        <td style="padding: 8px;">${statusIcon} ${warning.name}<br><small>${warning.hostname || warning.url}</small></td>
        <td style="padding: 8px;">${status}</td>
        <td style="padding: 8px;">${expiry}</td>
        <td style="padding: 8px;">${remaining}</td>
      </tr>
    `;
  });
  
  html += `
      </tbody>
    </table>
    
    <h3>📊 Tổng quan tất cả website:</h3>
    <table border="1" style="border-collapse: collapse; width: 100%;">
      <thead>
        <tr style="background-color: #f9f9f9;">
          <th style="padding: 8px;">Website</th>
          <th style="padding: 8px;">Trạng thái</th>
          <th style="padding: 8px;">Còn lại</th>
        </tr>
      </thead>
      <tbody>
  `;
  
  allResults.forEach(result => {
    const statusIcon = result.status === 'error' ? '❌' : 
                      !result.isValid ? '🚨' : 
                      result.daysRemaining <= 7 ? '⚠️' : '✅';
    const remaining = result.daysRemaining !== undefined ? 
                     `${result.daysRemaining} ngày` : 'N/A';
    
    html += `
      <tr>
        <td style="padding: 8px;">${statusIcon} ${result.name}</td>
        <td style="padding: 8px;">${result.status === 'error' ? 'Lỗi' : 'OK'}</td>
        <td style="padding: 8px;">${remaining}</td>
      </tr>
    `;
  });
  
  html += `
      </tbody>
    </table>
    
    <p style="margin-top: 20px; font-size: 12px; color: #666;">
      Được tạo bởi SSL Monitor - Google Apps Script<br>
      ${new Date().toLocaleString('vi-VN')}
    </p>
  `;
  
  return html;
}

/**
 * Gửi thông báo Slack
 */
function sendSlackNotification(warnings) {
  try {
    const message = {
      text: `🔒 *Cảnh báo SSL Certificate*\n📅 ${new Date().toLocaleString('vi-VN')}\n⚠️ ${warnings.length} website cần chú ý`,
      attachments: warnings.map(warning => ({
        color: warning.status === 'error' ? 'danger' :
               !warning.isValid ? 'danger' : 'warning',
        fields: [{
          title: warning.name,
          value: `${warning.hostname || warning.url}\n${
            warning.status === 'error' ? warning.error :
            `Hết hạn: ${warning.validTo?.toLocaleDateString('vi-VN')}\nCòn: ${warning.daysRemaining} ngày`
          }`,
          short: true
        }]
      }))
    };

    UrlFetchApp.fetch(CONFIG.slack.webhookUrl, {
      method: 'POST',
      contentType: 'application/json',
      payload: JSON.stringify(message)
    });

    console.log('📱 Đã gửi thông báo Slack');

  } catch (error) {
    console.error('❌ Lỗi gửi Slack:', error);
  }
}

/**
 * Lưu kết quả vào Google Sheets
 */
function saveToSheets(results) {
  try {
    const spreadsheet = SpreadsheetApp.openById(CONFIG.sheets.spreadsheetId);
    let sheet = spreadsheet.getSheetByName('SSL Monitor');

    // Tạo sheet mới nếu chưa có
    if (!sheet) {
      sheet = spreadsheet.insertSheet('SSL Monitor');
      // Thêm header
      sheet.getRange(1, 1, 1, 7).setValues([[
        'Thời gian', 'Website', 'URL', 'Trạng thái', 'Hết hạn', 'Còn lại (ngày)', 'Ghi chú'
      ]]);
      sheet.getRange(1, 1, 1, 7).setFontWeight('bold');
    }

    // Thêm dữ liệu mới
    const timestamp = new Date();
    results.forEach(result => {
      const row = [
        timestamp,
        result.name,
        result.hostname || result.url,
        result.status === 'error' ? 'Lỗi' : 'OK',
        result.validTo || '',
        result.daysRemaining || '',
        result.error || result.note || ''
      ];

      sheet.appendRow(row);
    });

    console.log('📊 Đã lưu kết quả vào Google Sheets');

  } catch (error) {
    console.error('❌ Lỗi lưu vào Sheets:', error);
  }
}

// ===== CÁC HÀM TIỆN ÍCH =====

/**
 * Thiết lập trigger tự động
 * Chạy hàm này một lần để thiết lập lịch kiểm tra hàng ngày
 */
function setupDailyTrigger() {
  // Xóa trigger cũ nếu có
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'checkAllSSLCertificates') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Tạo trigger mới - chạy hàng ngày lúc 9:00 AM
  ScriptApp.newTrigger('checkAllSSLCertificates')
    .timeBased()
    .everyDays(1)
    .atHour(9)
    .create();

  console.log('✅ Đã thiết lập trigger chạy hàng ngày lúc 9:00 AM');
}

/**
 * Thiết lập trigger chạy hàng tuần
 */
function setupWeeklyTrigger() {
  // Xóa trigger cũ
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'checkAllSSLCertificates') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  // Tạo trigger mới - chạy hàng tuần vào thứ 2 lúc 9:00 AM
  ScriptApp.newTrigger('checkAllSSLCertificates')
    .timeBased()
    .onWeekDay(ScriptApp.WeekDay.MONDAY)
    .atHour(9)
    .create();

  console.log('✅ Đã thiết lập trigger chạy hàng tuần vào thứ 2 lúc 9:00 AM');
}

/**
 * Xóa tất cả triggers
 */
function removeTriggers() {
  const triggers = ScriptApp.getProjectTriggers();
  triggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'checkAllSSLCertificates') {
      ScriptApp.deleteTrigger(trigger);
    }
  });

  console.log('🗑️ Đã xóa tất cả triggers');
}

/**
 * Kiểm tra một website cụ thể (để test)
 */
function testSingleWebsite() {
  const hostname = 'google.com'; // Thay đổi để test

  try {
    const result = checkSSLCertificate(hostname);
    console.log('Kết quả kiểm tra:', result);
    return result;
  } catch (error) {
    console.error('Lỗi:', error);
    return null;
  }
}

/**
 * Gửi email test
 */
function testEmail() {
  const testWarnings = [{
    name: 'Test Website',
    hostname: 'example.com',
    status: 'success',
    validTo: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    daysRemaining: 7,
    isValid: true
  }];

  sendEmailNotification(testWarnings, testWarnings);
  console.log('📧 Đã gửi email test');
}
