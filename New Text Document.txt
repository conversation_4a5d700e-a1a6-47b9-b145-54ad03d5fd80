/**
 * SSL Certificate Monitor - Google Apps Script
 * Theo dõi chứng chỉ SSL và gửi email cảnh báo khi sắp hết hạn
 */

// C<PERSON>u hình chính
const CONFIG = {
  // Danh sách website cần theo dõi
  websites: [
    'https://google.com',
    'https://github.com',
    'https://stackoverflow.com'
    // Thêm các website khác vào đây
  ],
  
  // Email nhận thông báo
  emailRecipients: [
    '<EMAIL>'
    // Thêm email khác nếu cần
  ],
  
  // Số ngày cảnh báo trước khi hết hạn
  warningDays: [30, 15, 7, 3, 1],
  
  // Tên người gửi
  senderName: 'SSL Monitor System'
};

/**
 * Hàm chính - kiểm tra tất cả website
 */
function checkAllSSLCertificates() {
  console.log('Bắt đầu kiểm tra SSL certificates...');
  
  const results = [];
  
  CONFIG.websites.forEach(website => {
    try {
      const sslInfo = getSSLCertificateInfo(website);
      if (sslInfo) {
        results.push(sslInfo);
        
        // Kiểm tra xem có cần gửi cảnh báo không
        const daysUntilExpiry = calculateDaysUntilExpiry(sslInfo.expiryDate);
        
        if (shouldSendWarning(daysUntilExpiry)) {
          sendExpiryWarning(sslInfo, daysUntilExpiry);
        }
      }
    } catch (error) {
      console.error(`Lỗi khi kiểm tra ${website}:`, error);
      
      // Gửi email báo lỗi
      sendErrorNotification(website, error.toString());
    }
  });
  
  // Lưu kết quả vào Google Sheets (tùy chọn)
  saveResultsToSheet(results);
  
  console.log('Hoàn thành kiểm tra SSL certificates');
  return results;
}

/**
 * Lấy thông tin SSL certificate
 */
function getSSLCertificateInfo(url) {
  const domain = url.replace(/^https?:\/\//, '').split('/')[0];
  
  // Thử nhiều phương pháp khác nhau
  const methods = [
    () => getSSLInfoFromSSLLabs(domain),
    () => getSSLInfoFromBadSSL(domain),
    () => getSSLInfoFromCertSpotter(domain),
    () => getSSLInfoFallback(url)
  ];
  
  for (let i = 0; i < methods.length; i++) {
    try {
      console.log(`Trying method ${i + 1} for ${domain}`);
      const result = methods[i]();
      if (result && result.domain) {
        console.log(`Method ${i + 1} succeeded for ${domain}`);
        return result;
      }
    } catch (error) {
      console.log(`Method ${i + 1} failed for ${domain}:`, error.toString());
      continue;
    }
  }
  
  console.error(`All methods failed for ${domain}`);
  return null;
}

/**
 * Sử dụng SSL Labs API (phương pháp 1)
 */
function getSSLInfoFromSSLLabs(domain) {
  try {
    const apiUrl = `https://api.ssllabs.com/api/v3/info`;
    
    // Kiểm tra API có sẵn không
    const infoResponse = UrlFetchApp.fetch(apiUrl, {
      muteHttpExceptions: true,
      headers: {
        'User-Agent': 'SSL Monitor Script'
      }
    });
    
    if (infoResponse.getResponseCode() !== 200) {
      throw new Error('SSL Labs API not available');
    }
    
    // Bắt đầu phân tích
    const analyzeUrl = `https://api.ssllabs.com/api/v3/analyze?host=${domain}&publish=off&startNew=on&all=done&ignoreMismatch=on`;
    
    const response = UrlFetchApp.fetch(analyzeUrl, {
      muteHttpExceptions: true,
      headers: {
        'User-Agent': 'SSL Monitor Script'
      }
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`SSL Labs API error: ${response.getResponseCode()}`);
    }
    
    const data = JSON.parse(response.getContentText());
    
    if (data.status === 'READY' && data.endpoints && data.endpoints.length > 0) {
      const endpoint = data.endpoints[0];
      
      if (endpoint.details && endpoint.details.cert) {
        const cert = endpoint.details.cert;
        
        return {
          domain: domain,
          issuer: cert.issuerLabel || 'Unknown',
          expiryDate: new Date(cert.notAfter),
          isValid: endpoint.grade !== 'T' && endpoint.grade !== 'M',
          daysRemaining: Math.ceil((new Date(cert.notAfter) - new Date()) / (1000 * 60 * 60 * 24)),
          checkTime: new Date(),
          source: 'SSL Labs'
        };
      }
    }
    
    throw new Error('SSL Labs analysis incomplete or failed');
    
  } catch (error) {
    throw new Error(`SSL Labs method failed: ${error.message}`);
  }
}

/**
 * Sử dụng BadSSL.com API alternative (phương pháp 2)
 */
function getSSLInfoFromBadSSL(domain) {
  try {
    // Sử dụng Mozilla's SSL Configuration Generator API
    const apiUrl = `https://ssl-config.mozilla.org/guidelines/5.6.json`;
    
    // Thực hiện request đơn giản để kiểm tra SSL
    const testUrl = `https://${domain}`;
    const response = UrlFetchApp.fetch(testUrl, {
      muteHttpExceptions: true,
      followRedirects: false,
      validateHttpsCertificates: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SSL-Monitor/1.0)'
      }
    });
    
    // Nếu request thành công, SSL đang hoạt động
    if (response.getResponseCode() < 500) {
      return {
        domain: domain,
        issuer: 'Unknown (HTTP Test)',
        expiryDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // Giả định 60 ngày
        isValid: true,
        daysRemaining: 60,
        checkTime: new Date(),
        source: 'HTTP Test',
        note: 'Estimated expiry date - please verify manually'
      };
    } else {
      throw new Error(`Website returned error: ${response.getResponseCode()}`);
    }
    
  } catch (error) {
    throw new Error(`BadSSL method failed: ${error.message}`);
  }
}

/**
 * Sử dụng Certificate Transparency logs (phương pháp 3)
 */
function getSSLInfoFromCertSpotter(domain) {
  try {
    const apiUrl = `https://crt.sh/?q=${domain}&output=json&limit=1`;
    
    const response = UrlFetchApp.fetch(apiUrl, {
      muteHttpExceptions: true,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'SSL Monitor Script'
      }
    });
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`CRT.sh API error: ${response.getResponseCode()}`);
    }
    
    const data = JSON.parse(response.getContentText());
    
    if (data && data.length > 0) {
      const cert = data[0];
      
      return {
        domain: domain,
        issuer: cert.issuer_name || 'Unknown',
        expiryDate: new Date(cert.not_after),
        isValid: new Date(cert.not_after) > new Date(),
        daysRemaining: Math.ceil((new Date(cert.not_after) - new Date()) / (1000 * 60 * 60 * 24)),
        checkTime: new Date(),
        source: 'Certificate Transparency'
      };
    }
    
    throw new Error('No certificates found in CT logs');
    
  } catch (error) {
    throw new Error(`CertSpotter method failed: ${error.message}`);
  }
}

/**
 * Phương pháp dự phòng cuối cùng
 */
function getSSLInfoFallback(url) {
  try {
    const domain = url.replace(/^https?:\/\//, '').split('/')[0];
    
    // Chỉ thực hiện GET request đơn giản
    const response = UrlFetchApp.fetch(url, {
      muteHttpExceptions: true,
      followRedirects: true,
      validateHttpsCertificates: true,
      headers: {
        'User-Agent': 'Mozilla/5.0 (compatible; SSL-Monitor/1.0)'
      }
    });
    
    // Nếu request thành công và HTTPS hoạt động
    if (response.getResponseCode() < 500 && url.startsWith('https://')) {
      return {
        domain: domain,
        issuer: 'Unknown (Fallback Test)',
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // Giả định 30 ngày
        isValid: true,
        daysRemaining: 30,
        checkTime: new Date(),
        source: 'Fallback Method',
        note: 'SSL is working but expiry date is estimated - please verify manually'
      };
    } else {
      throw new Error(`Website test failed: ${response.getResponseCode()}`);
    }
    
  } catch (error) {
    console.error(`Fallback method failed for ${url}:`, error);
    
    // Trả về thông tin cơ bản cho việc tracking
    const domain = url.replace(/^https?:\/\//, '').split('/')[0];
    return {
      domain: domain,
      issuer: 'Error - Could not verify',
      expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 ngày để cảnh báo
      isValid: false,
      daysRemaining: 7,
      checkTime: new Date(),
      source: 'Error Handler',
      note: `Error: ${error.message}`
    };
  }
}

/**
 * Tính số ngày còn lại đến khi hết hạn
 */
function calculateDaysUntilExpiry(expiryDate) {
  const now = new Date();
  const diffTime = expiryDate - now;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

/**
 * Kiểm tra xem có nên gửi cảnh báo không
 */
function shouldSendWarning(daysUntilExpiry) {
  return CONFIG.warningDays.includes(daysUntilExpiry) || daysUntilExpiry <= 0;
}

/**
 * Gửi email cảnh báo hết hạn
 */
function sendExpiryWarning(sslInfo, daysUntilExpiry) {
  const subject = daysUntilExpiry <= 0 
    ? `🚨 KHẨN CẤP: SSL Certificate đã hết hạn - ${sslInfo.domain}`
    : `⚠️ CẢNH BÁO: SSL Certificate sắp hết hạn - ${sslInfo.domain}`;
  
  const htmlBody = createWarningEmailHtml(sslInfo, daysUntilExpiry);
  const plainBody = createWarningEmailPlain(sslInfo, daysUntilExpiry);
  
  CONFIG.emailRecipients.forEach(email => {
    try {
      GmailApp.sendEmail(
        email,
        subject,
        plainBody,
        {
          htmlBody: htmlBody,
          name: CONFIG.senderName
        }
      );
      
      console.log(`Đã gửi cảnh báo tới ${email} cho domain ${sslInfo.domain}`);
    } catch (error) {
      console.error(`Lỗi khi gửi email tới ${email}:`, error);
    }
  });
}

/**
 * Tạo nội dung email HTML
 */
function createWarningEmailHtml(sslInfo, daysUntilExpiry) {
  const isExpired = daysUntilExpiry <= 0;
  const urgencyColor = isExpired ? '#dc3545' : daysUntilExpiry <= 7 ? '#fd7e14' : '#ffc107';
  
  return `
    <html>
      <body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: ${urgencyColor}; color: white; padding: 20px; text-align: center;">
          <h1>${isExpired ? '🚨 SSL Certificate Đã Hết Hạn' : '⚠️ SSL Certificate Sắp Hết Hạn'}</h1>
        </div>
        
        <div style="padding: 20px; background-color: #f8f9fa;">
          <h2>Thông Tin Chi Tiết:</h2>
          <table style="width: 100%; border-collapse: collapse;">
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background-color: #e9ecef;"><strong>Domain:</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd;">${sslInfo.domain}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background-color: #e9ecef;"><strong>Ngày hết hạn:</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd;">${sslInfo.expiryDate.toLocaleDateString('vi-VN')}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background-color: #e9ecef;"><strong>Số ngày còn lại:</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd; color: ${urgencyColor};">
                <strong>${daysUntilExpiry} ngày</strong>
              </td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background-color: #e9ecef;"><strong>Nhà phát hành:</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd;">${sslInfo.issuer}</td>
            </tr>
            <tr>
              <td style="padding: 8px; border: 1px solid #ddd; background-color: #e9ecef;"><strong>Thời gian kiểm tra:</strong></td>
              <td style="padding: 8px; border: 1px solid #ddd;">${sslInfo.checkTime.toLocaleString('vi-VN')}</td>
            </tr>
          </table>
          
          <div style="margin-top: 20px; padding: 15px; background-color: ${isExpired ? '#f8d7da' : '#fff3cd'}; border-radius: 5px;">
            <h3>📋 Hành Động Cần Thực Hiện:</h3>
            <ul>
              <li>Kiểm tra và gia hạn SSL certificate ngay lập tức</li>
              <li>Liên hệ với nhà cung cấp hosting/SSL</li>
              <li>Backup dữ liệu quan trọng</li>
              ${isExpired ? '<li><strong>Khẩn cấp:</strong> Website có thể không truy cập được an toàn</li>' : ''}
            </ul>
          </div>
        </div>
        
        <div style="padding: 10px; text-align: center; background-color: #6c757d; color: white;">
          <small>Email tự động từ SSL Monitor System</small>
        </div>
      </body>
    </html>
  `;
}

/**
 * Tạo nội dung email văn bản thuần
 */
function createWarningEmailPlain(sslInfo, daysUntilExpiry) {
  const isExpired = daysUntilExpiry <= 0;
  
  return `
SSL CERTIFICATE ${isExpired ? 'ĐÃ HẾT HẠN' : 'SẮP HẾT HẠN'}

Domain: ${sslInfo.domain}
Ngày hết hạn: ${sslInfo.expiryDate.toLocaleDateString('vi-VN')}
Số ngày còn lại: ${daysUntilExpiry} ngày
Nhà phát hành: ${sslInfo.issuer}
Thời gian kiểm tra: ${sslInfo.checkTime.toLocaleString('vi-VN')}

HÀNH ĐỘNG CẦN THỰC HIỆN:
- Kiểm tra và gia hạn SSL certificate ngay lập tức
- Liên hệ với nhà cung cấp hosting/SSL
- Backup dữ liệu quan trọng
${isExpired ? '- KHẨN CẤP: Website có thể không truy cập được an toàn' : ''}

---
Email tự động từ SSL Monitor System
  `.trim();
}

/**
 * Gửi thông báo lỗi
 */
function sendErrorNotification(website, errorMessage) {
  const subject = `❌ Lỗi kiểm tra SSL Certificate - ${website}`;
  const body = `
Có lỗi xảy ra khi kiểm tra SSL certificate cho website: ${website}

Chi tiết lỗi: ${errorMessage}

Vui lòng kiểm tra lại cấu hình hoặc liên hệ quản trị viên.

---
Email tự động từ SSL Monitor System
  `;
  
  CONFIG.emailRecipients.forEach(email => {
    try {
      GmailApp.sendEmail(email, subject, body, { name: CONFIG.senderName });
    } catch (error) {
      console.error(`Lỗi khi gửi email báo lỗi tới ${email}:`, error);
    }
  });
}

/**
 * Lưu kết quả vào Google Sheets (tùy chọn)
 */
function saveResultsToSheet(results) {
  try {
    const spreadsheetId = PropertiesService.getScriptProperties().getProperty('SSL_MONITOR_SHEET_ID');
    
    if (!spreadsheetId) {
      console.log('Không có Spreadsheet ID. Bỏ qua việc lưu vào Sheets.');
      return;
    }
    
    const spreadsheet = SpreadsheetApp.openById(spreadsheetId);
    let sheet = spreadsheet.getSheetByName('SSL Monitor Log');
    
    if (!sheet) {
      sheet = spreadsheet.insertSheet('SSL Monitor Log');
      // Tạo header
      sheet.getRange(1, 1, 1, 7).setValues([
        ['Thời gian kiểm tra', 'Domain', 'Ngày hết hạn', 'Số ngày còn lại', 'Nhà phát hành', 'Trạng thái', 'Ghi chú']
      ]);
    }
    
    // Thêm dữ liệu mới
    results.forEach(result => {
      const daysRemaining = calculateDaysUntilExpiry(result.expiryDate);
      sheet.appendRow([
        result.checkTime,
        result.domain,
        result.expiryDate,
        daysRemaining,
        result.issuer,
        result.isValid ? 'Hợp lệ' : 'Không hợp lệ',
        result.note || ''
      ]);
    });
    
  } catch (error) {
    console.error('Lỗi khi lưu vào Sheets:', error);
  }
}

/**
 * Thiết lập trigger tự động (chạy 1 lần để cài đặt)
 */
function setupAutomaticTrigger() {
  // Xóa các trigger cũ
  const existingTriggers = ScriptApp.getProjectTriggers();
  existingTriggers.forEach(trigger => {
    if (trigger.getHandlerFunction() === 'checkAllSSLCertificates') {
      ScriptApp.deleteTrigger(trigger);
    }
  });
  
  // Tạo trigger mới - chạy hàng ngày lúc 9:00 AM
  ScriptApp.newTrigger('checkAllSSLCertificates')
    .timeBased()
    .everyDays(1)
    .atHour(9)
    .create();
  
  console.log('Đã thiết lập trigger tự động chạy hàng ngày lúc 9:00 AM');
}

/**
 * Thiết lập Google Sheets để lưu log (tùy chọn)
 */
function setupGoogleSheets() {
  const spreadsheet = SpreadsheetApp.create('SSL Certificate Monitor Log');
  const spreadsheetId = spreadsheet.getId();
  
  PropertiesService.getScriptProperties().setProperty('SSL_MONITOR_SHEET_ID', spreadsheetId);
  
  console.log(`Đã tạo Google Sheets: ${spreadsheet.getUrl()}`);
  console.log(`Spreadsheet ID: ${spreadsheetId}`);
  
  return spreadsheetId;
}

/**
 * Hàm test đơn giản để kiểm tra 1 website cụ thể
 */
function testSingleWebsite(url = 'https://google.com') {
  console.log(`🔍 Testing SSL for: ${url}`);
  console.log('=====================================');
  
  try {
    const sslInfo = getSSLCertificateInfo(url);
    
    if (sslInfo) {
      console.log('✅ SSL Info retrieved successfully:');
      console.log(`   Domain: ${sslInfo.domain}`);
      console.log(`   Issuer: ${sslInfo.issuer}`);
      console.log(`   Expiry Date: ${sslInfo.expiryDate.toLocaleDateString('vi-VN')}`);
      console.log(`   Days Remaining: ${sslInfo.daysRemaining}`);
      console.log(`   Is Valid: ${sslInfo.isValid}`);
      console.log(`   Source: ${sslInfo.source}`);
      console.log(`   Check Time: ${sslInfo.checkTime.toLocaleString('vi-VN')}`);
      
      if (sslInfo.note) {
        console.log(`   Note: ${sslInfo.note}`);
      }
      
      const daysUntilExpiry = calculateDaysUntilExpiry(sslInfo.expiryDate);
      console.log(`   Calculated Days Until Expiry: ${daysUntilExpiry}`);
      
      if (shouldSendWarning(daysUntilExpiry)) {
        console.log('⚠️ This certificate would trigger a warning email');
      } else {
        console.log('✅ Certificate is healthy, no warning needed');
      }
      
    } else {
      console.log('❌ Failed to retrieve SSL information');
    }
    
  } catch (error) {
    console.error(`❌ Error testing ${url}:`, error.toString());
  }
  
  console.log('=====================================');
  return sslInfo;
}

/**
 * Test tất cả website trong cấu hình
 */
function testAllWebsites() {
  console.log('🚀 Testing all configured websites...');
  console.log('=====================================');
  
  const results = [];
  
  CONFIG.websites.forEach((website, index) => {
    console.log(`\n📍 Testing ${index + 1}/${CONFIG.websites.length}: ${website}`);
    
    try {
      const sslInfo = getSSLCertificateInfo(website);
      if (sslInfo) {
        results.push(sslInfo);
        const daysUntilExpiry = calculateDaysUntilExpiry(sslInfo.expiryDate);
        console.log(`   ✅ Success - ${daysUntilExpiry} days remaining`);
      } else {
        console.log(`   ❌ Failed to get SSL info`);
      }
    } catch (error) {
      console.log(`   ❌ Error: ${error.toString()}`);
    }
  });
  
  console.log(`\n📊 Summary: ${results.length}/${CONFIG.websites.length} websites checked successfully`);
  
  // Hiển thị danh sách cần cảnh báo
  const needWarning = results.filter(result => {
    const days = calculateDaysUntilExpiry(result.expiryDate);
    return shouldSendWarning(days);
  });
  
  if (needWarning.length > 0) {
    console.log(`\n⚠️ Websites needing warnings: ${needWarning.length}`);
    needWarning.forEach(site => {
      const days = calculateDaysUntilExpiry(site.expiryDate);
      console.log(`   - ${site.domain}: ${days} days remaining`);
    });
  } else {
    console.log(`\n✅ All certificates are healthy!`);
  }
  
  return results;
}

/**
 * Test gửi email (không gửi thật, chỉ tạo nội dung)
 */
function testEmailContent(domain = 'example.com') {
  const mockSSLInfo = {
    domain: domain,
    issuer: 'Let\'s Encrypt Authority X3',
    expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 ngày nữa
    isValid: true,
    daysRemaining: 5,
    checkTime: new Date(),
    source: 'Test Mode'
  };
  
  const daysUntilExpiry = 5;
  
  console.log('📧 Email Subject:');
  const subject = `⚠️ CẢNH BÁO: SSL Certificate sắp hết hạn - ${mockSSLInfo.domain}`;
  console.log(subject);
  
  console.log('\n📧 Email Content (Plain Text):');
  const plainContent = createWarningEmailPlain(mockSSLInfo, daysUntilExpiry);
  console.log(plainContent);
  
  console.log('\n📧 Email Content (HTML) - First 500 chars:');
  const htmlContent = createWarningEmailHtml(mockSSLInfo, daysUntilExpiry);
  console.log(htmlContent.substring(0, 500) + '...');
  
  return {
    subject: subject,
    plain: plainContent,
    html: htmlContent
  };
}