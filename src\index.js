#!/usr/bin/env node

require('dotenv').config();
const cron = require('node-cron');
const fs = require('fs');
const path = require('path');

const SSLChecker = require('./sslChecker');
const NotificationManager = require('./notificationManager');
const logger = require('./logger');

class SSLMonitor {
  constructor() {
    this.config = this.loadConfig();
    this.notificationManager = new NotificationManager(this.config.notifications);
  }

  loadConfig() {
    try {
      const configPath = path.join(process.cwd(), 'config', 'config.json');
      const configData = fs.readFileSync(configPath, 'utf8');
      const config = JSON.parse(configData);

      // Override với environment variables nếu có
      if (process.env.EMAIL_HOST) {
        config.notifications.methods.email.smtp.host = process.env.EMAIL_HOST;
      }
      if (process.env.EMAIL_USER) {
        config.notifications.methods.email.smtp.auth.user = process.env.EMAIL_USER;
        config.notifications.methods.email.from = process.env.EMAIL_USER;
      }
      if (process.env.EMAIL_PASS) {
        config.notifications.methods.email.smtp.auth.pass = process.env.EMAIL_PASS;
      }
      if (process.env.EMAIL_TO) {
        config.notifications.methods.email.to = process.env.EMAIL_TO.split(',');
      }
      if (process.env.WEBHOOK_URL) {
        config.notifications.methods.webhook.url = process.env.WEBHOOK_URL;
      }

      return config;
    } catch (error) {
      logger.error('Lỗi đọc file config:', error);
      process.exit(1);
    }
  }

  async checkAllSSL() {
    try {
      logger.info('Bắt đầu kiểm tra SSL certificates...');
      
      const results = await SSLChecker.checkMultiple(this.config.websites);
      const warnings = SSLChecker.filterWarnings(results, this.config.notifications.warningDays);

      logger.info(`Đã kiểm tra ${results.length} website, ${warnings.length} cần cảnh báo`);

      // Gửi thông báo
      await this.notificationManager.sendAllNotifications(warnings);

      // Log chi tiết
      this.logResults(results);

      return { results, warnings };
    } catch (error) {
      logger.error('Lỗi trong quá trình kiểm tra SSL:', error);
      throw error;
    }
  }

  logResults(results) {
    results.forEach(result => {
      if (result.status === 'success') {
        logger.info(`${result.name}: ${result.daysRemaining} ngày còn lại (hết hạn: ${result.validTo.toLocaleDateString('vi-VN')})`);
      } else {
        logger.error(`${result.name}: ${result.error}`);
      }
    });
  }

  start() {
    logger.info('🚀 SSL Monitor đã khởi động');
    logger.info(`📅 Lịch kiểm tra: ${this.config.schedule.cron}`);
    logger.info(`🌐 Theo dõi ${this.config.websites.filter(w => w.enabled).length} website`);

    // Chạy ngay lần đầu
    this.checkAllSSL().catch(error => {
      logger.error('Lỗi kiểm tra SSL lần đầu:', error);
    });

    // Lên lịch kiểm tra định kỳ
    cron.schedule(this.config.schedule.cron, async () => {
      logger.info('⏰ Bắt đầu kiểm tra SSL theo lịch');
      try {
        await this.checkAllSSL();
      } catch (error) {
        logger.error('Lỗi kiểm tra SSL theo lịch:', error);
      }
    }, {
      timezone: this.config.schedule.timezone
    });

    logger.info('✅ SSL Monitor đang chạy. Nhấn Ctrl+C để dừng.');
  }

  async checkOnce() {
    logger.info('🔍 Kiểm tra SSL một lần...');
    try {
      const { results, warnings } = await this.checkAllSSL();
      
      console.log('\n📊 KẾT QUẢ KIỂM TRA:');
      console.log(`✅ Tổng số website: ${results.length}`);
      console.log(`⚠️  Cần cảnh báo: ${warnings.length}`);
      console.log(`✅ Bình thường: ${results.length - warnings.length}`);
      
      return { results, warnings };
    } catch (error) {
      logger.error('Lỗi kiểm tra SSL:', error);
      process.exit(1);
    }
  }
}

// Xử lý command line arguments
const args = process.argv.slice(2);

if (args.includes('--check') || args.includes('-c')) {
  // Chạy kiểm tra một lần
  const monitor = new SSLMonitor();
  monitor.checkOnce().then(() => {
    process.exit(0);
  });
} else if (args.includes('--help') || args.includes('-h')) {
  console.log(`
SSL Monitor - Theo dõi chứng chỉ SSL

Cách sử dụng:
  npm start              Chạy monitor liên tục theo lịch
  npm run check          Kiểm tra một lần và thoát
  node src/index.js -c   Kiểm tra một lần
  node src/index.js -h   Hiển thị trợ giúp

Cấu hình:
  - Chỉnh sửa file config/config.json để thêm website và cấu hình thông báo
  - Tạo file .env từ .env.example để cấu hình email và webhook
  `);
  process.exit(0);
} else {
  // Chạy monitor liên tục
  const monitor = new SSLMonitor();
  monitor.start();

  // Xử lý tín hiệu dừng
  process.on('SIGINT', () => {
    logger.info('🛑 Đang dừng SSL Monitor...');
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    logger.info('🛑 Đang dừng SSL Monitor...');
    process.exit(0);
  });
}

module.exports = SSLMonitor;
